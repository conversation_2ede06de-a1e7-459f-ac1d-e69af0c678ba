<template>
  <view class="webview-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载充值页面...</text>
    </view>

    <!-- WebView -->
    <web-view v-if="webviewUrl" :src="webviewUrl" @message="handleMessage" @load="handleLoad" @error="handleError"
      class="webview"></web-view>
  </view>
</template>

<script>
import { isLogin, getToken, getUserId } from "@/utils/auth.js";
import { getMenuConfig } from "@/api/menu.js";

export default {
  data() {
    return {
      loading: true,
      webviewUrl: "",
      baseUrl: "", // PC端的基础URL
    };
  },
  onLoad(options) {
    // 获取传递的参数
    this.initWebview(options);
  },
  methods: {
    // 初始化webview
    async initWebview(options) {
      // 检查登录状态
      if (!isLogin()) {
        uni.showToast({
          title: "请先登录",
          icon: "error",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
        return;
      }

      try {
        // 通过接口获取充值服务配置
        await this.getCreditUrlFromApi();

        // 获取用户token和ID
        const token = getToken();
        const userId = getUserId();

        // 构建webview URL (注意Vue Router的hash模式)
        const page = options.page || "warning"; // 默认显示温馨提示页面
        let routePath = "";

        switch (page) {
          case "warning":
            routePath = "/userCredit/warning";
            break;
          case "credit":
            routePath = "/userCredit";
            break;
          default:
            routePath = "/userCredit/warning";
        }

        // 构建完整的hash路由URL
        let targetUrl = `${this.baseUrl}/#${routePath}`;
        console.log(targetUrl);

        // 添加token和用户ID参数 (小程序兼容方式)
        const params = [];
        params.push(`token=${encodeURIComponent(token)}`);
        params.push(`userId=${encodeURIComponent(userId)}`);
        params.push(`source=miniprogram`); // 标识来源

        this.webviewUrl = `${targetUrl}?${params.join('&')}`;

        console.log("WebView URL:", this.webviewUrl);
      } catch (error) {
        console.error("初始化webview失败:", error);
        uni.showToast({
          title: "加载失败，请重试",
          icon: "error",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    // 通过接口获取充值服务URL配置
    async getCreditUrlFromApi() {
      try {
        const res = await getMenuConfig();

        if (res.data && res.data.code === 200) {
          const menuConfig = res.data.data || [];

          // 查找 cornCode 为 "credit-url" 的配置项
          const creditUrlConfig = menuConfig.find(item => item.cornCode === "credit-url");

          if (creditUrlConfig) {
            // 使用配置的URL
            this.baseUrl = creditUrlConfig.cornName;
            console.log("从接口获取到充值服务URL:", this.baseUrl);
          } else {
            // 没有找到配置，使用默认URL
            this.baseUrl = "https://www.airui-etc.cn";
            console.log("未找到充值服务URL配置，使用默认URL:", this.baseUrl);
          }
        } else {
          // 接口调用失败，使用默认URL
          this.baseUrl = "https://www.airui-etc.cn";
          console.warn("获取充值服务配置失败，使用默认URL:", this.baseUrl);
        }
      } catch (error) {
        // 出错时使用默认URL
        this.baseUrl = "https://www.airui-etc.cn";
        console.error("获取充值服务配置出错:", error);
        throw error;
      }
    },



    // 处理webview加载完成
    handleLoad(e) {
      console.log("WebView loaded:", e);
      this.loading = false;
    },

    // 处理webview错误
    handleError(e) {
      console.error("WebView error:", e);
      this.loading = false;

      uni.showModal({
        title: "加载失败",
        content: "页面加载失败，请检查网络连接后重试",
        showCancel: true,
        cancelText: "返回",
        confirmText: "重试",
        success: (res) => {
          if (res.confirm) {
            // 重新加载
            this.loading = true;
            this.initWebview({});
          } else {
            // 返回上一页
            uni.navigateBack();
          }
        }
      });
    },

    // 处理来自webview的消息
    handleMessage(e) {
      console.log("Received message from webview:", e.detail.data);

      // 兼容不同的消息格式
      let data = e.detail.data;
      if (Array.isArray(data) && data.length > 0) {
        data = data[0]; // 获取第一个消息
      }

      if (!data) {
        console.warn("收到空消息");
        return;
      }

      console.log("处理消息类型:", data.type, "数据:", data);

      switch (data.type) {
        case "payment":
          // 处理支付请求
          this.handlePayment(data.paymentData);
          break;
        case "navigation":
          // 处理页面导航
          this.handleNavigation(data);
          break;
        case "close":
          // 关闭webview
          uni.navigateBack();
          break;
        default:
          console.log("Unknown message type:", data.type);
      }
    },

    // 处理支付
    handlePayment(paymentData) {
      console.log("Processing payment:", paymentData);

      if (!paymentData) {
        console.error("支付数据为空");
        uni.showToast({
          title: "支付数据错误",
          icon: "error",
        });
        return;
      }

      // 验证必要的支付参数
      const requiredFields = ['appid', 'timestamp', 'noncestr', 'package', 'signtype', 'paysign'];
      const missingFields = requiredFields.filter(field => !paymentData[field]);

      if (missingFields.length > 0) {
        console.error("缺少必要的支付参数:", missingFields);
        uni.showToast({
          title: "支付参数不完整",
          icon: "error",
        });
        return;
      }

      console.log("支付参数验证通过，开始调起微信支付");

      // 调起微信支付
      uni.requestPayment({
        appId: paymentData.appid,
        timeStamp: paymentData.timestamp,
        nonceStr: paymentData.noncestr,
        package: paymentData.package,
        signType: paymentData.signtype,
        paySign: paymentData.paysign,
        success: (res) => {
          console.log("Payment success:", res);
          uni.showToast({
            title: "支付成功",
            icon: "success",
          });

          // 支付成功后可以通知webview页面
          this.notifyWebviewPaymentResult('success', res);

          // 支付成功后返回首页
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/my/my",
            });
          }, 1500);
        },
        fail: (err) => {
          console.error("Payment failed:", err);

          // 通知webview页面支付失败
          this.notifyWebviewPaymentResult('fail', err);

          if (err.errMsg === "requestPayment:fail cancel") {
            uni.showToast({
              title: "您已取消支付",
              icon: "none",
            });
          } else {
            uni.showToast({
              title: "支付失败，请重试",
              icon: "error",
            });
          }
        },
        complete: (res) => {
          console.log("Payment complete:", res);
        }
      });
    },

    // 通知webview页面支付结果
    notifyWebviewPaymentResult(status, data) {
      // 这里可以向webview发送支付结果消息
      // 但由于webview的限制，通常只能通过URL参数或其他方式传递
      console.log(`支付${status}:`, data);
    },

    // 处理页面导航
    handleNavigation(data) {
      switch (data.action) {
        case "back":
          uni.navigateBack();
          break;
        case "home":
          uni.switchTab({
            url: "/pages/my/my",
          });
          break;
        default:
          console.log("Unknown navigation action:", data.action);
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.webview-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.webview {
  width: 100%;
  height: 100%;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
